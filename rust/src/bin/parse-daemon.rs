/*
cd ~/main/eterna/rust/ && \
cargo run --bin parse-daemon -- \
--sensor-names Sensor1 Sensor2 Sensor3 \
--log-date 2020-01-02
*/

use clap::Parser;

#[derive(Parser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long = "sensor-names", num_args = 1..)]
    sensor_names: Vec<String>,

    #[arg(long = "log-date")]
    log_date: String,
}

fn main() {
    let args = Args::parse();
    println!("Sensor names: {:?}", args.sensor_names);
    println!("Log date: {:?}", args.log_date);
}
