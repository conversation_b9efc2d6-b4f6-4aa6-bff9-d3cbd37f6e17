/*
cd ~/main/eterna/rust/ && \
cargo run --bin parse-daemon -- \
--sensor-names Sensor1 Sensor2 Sensor3 \
--log-date 2020-01-02
*/

use clap::Parser;

#[derive(Parser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long = "source-log")]
    source_log: String,
    // /FOO/BAR/BAZ/logs/2025-06-13--Fri.log

    #[arg(long = "log-date")]
    log_date: String,
    // 2020-01-02

    #[arg(long = "sensor-names", num_args = 1..)]
    sensor_names: Vec<String>,
    // ["Sensor-1", "Sensor-2", "Sensor-3"]
}

fn main() {
    let args = Args::parse();
    println!("Source log: {:?}", args.source_log);
    println!("Log date: {:?}", args.log_date);
    println!("Sensor names: {:?}", args.sensor_names);
}
